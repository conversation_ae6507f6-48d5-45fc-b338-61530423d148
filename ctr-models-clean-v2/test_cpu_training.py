#!/usr/bin/env python3
"""
Test script to run improved GAN training on CPU to isolate CUDA issues
"""

import os
import sys
import logging
from pathlib import Path

# Add project root to path
ROOT_DIR = Path(__file__).resolve().parent
sys.path.append(str(ROOT_DIR))

def test_cpu_training():
    """Test training on CPU with very small dataset"""
    print("🧪 Testing Improved GAN Training on CPU")
    print("=" * 50)
    
    # Import here to avoid CUDA initialization
    from src.gan.improved_data_prep import prepare_improved_gan_data, create_improved_dataloader
    from src.gan.models import ImprovedConditionalGenerator, ImprovedDiscriminator
    from src.gan.improved_trainer import ImprovedGANTrainer, ImprovedGANConfig
    from src.data_process.utils import seed_everything
    
    # Set seed
    seed_everything(2024)
    
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    try:
        # 1. Load very small dataset
        logger.info("Loading small dataset...")
        processed_data, processor = prepare_improved_gan_data(
            dataset_name='Criteo',
            data_dir='/data/Criteo_x4',
            output_dir='/tmp/cpu_test_gan',
            train_file='train.csv',
            use_cache=False,
            max_samples=1000,  # Very small
            max_vocab_size=1000,
            min_freq=1
        )
        
        logger.info(f"Data loaded: {processed_data.shape}")
        
        # 2. Create dataloader
        config = ImprovedGANConfig()
        config.batch_size = 32  # Small batch
        config.epochs = 2  # Just 2 epochs
        
        dataloader = create_improved_dataloader(processed_data, config)
        logger.info(f"Dataloader created: {len(dataloader)} batches")
        
        # 3. Create models
        feature_info = processor.get_feature_info()
        logger.info(f"Feature info: {feature_info}")
        
        generator = ImprovedConditionalGenerator(
            noise_dim=config.noise_dim,
            numeric_features=feature_info['numeric_features'],
            categorical_features=feature_info['categorical_features'],
            vocab_sizes=feature_info['vocab_sizes'],
            embedding_dim=config.embedding_dim,
            ctr_embedding_dim=config.ctr_embedding_dim
        )
        
        discriminator = ImprovedDiscriminator(
            numeric_features=feature_info['numeric_features'],
            categorical_features=feature_info['categorical_features'],
            vocab_sizes=feature_info['vocab_sizes'],
            embedding_dim=config.embedding_dim,
            hidden_dim=config.discriminator_hidden_dim
        )
        
        logger.info("Models created successfully")
        
        # 4. Create trainer with FORCED CPU
        trainer = ImprovedGANTrainer(generator, discriminator, config, force_cpu=True)
        logger.info("Trainer created on CPU")
        
        # 5. Test one training step
        logger.info("Testing one training step...")
        
        for batch_idx, batch in enumerate(dataloader):
            logger.info(f"Processing batch {batch_idx}")
            
            try:
                metrics = trainer.train_step(batch)
                logger.info(f"✅ Training step successful! Metrics: {metrics}")
                break  # Just test one batch
                
            except Exception as e:
                logger.error(f"❌ Training step failed: {e}")
                import traceback
                traceback.print_exc()
                return False
        
        # 6. Test a few more steps
        logger.info("Testing a few more training steps...")
        step_count = 0
        for batch_idx, batch in enumerate(dataloader):
            if step_count >= 3:  # Test 3 steps total
                break
                
            try:
                metrics = trainer.train_step(batch)
                logger.info(f"Step {step_count}: D_loss={metrics['d_loss']:.4f}, G_loss={metrics['g_loss']:.4f}")
                step_count += 1
                
            except Exception as e:
                logger.error(f"❌ Training step {step_count} failed: {e}")
                return False
        
        logger.info("🎉 CPU training test PASSED!")
        logger.info("The issue is likely CUDA-specific, not in the core logic")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ CPU training test FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    success = test_cpu_training()
    
    if success:
        print("\n✅ CPU Training Test Results:")
        print("- Core training logic works correctly")
        print("- Issue is CUDA-specific")
        print("- Try running with --force_cpu flag:")
        print("  python train_improved_gan.py --force_cpu --debug ...")
        print("\n🔧 For CUDA debugging:")
        print("- Check categorical index ranges vs embedding sizes")
        print("- Run with CUDA_LAUNCH_BLOCKING=1 for precise error location")
        print("- Consider using smaller batch sizes on GPU")
    else:
        print("\n❌ CPU Training Test Failed:")
        print("- There are fundamental issues in the training logic")
        print("- Fix these before addressing CUDA issues")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
