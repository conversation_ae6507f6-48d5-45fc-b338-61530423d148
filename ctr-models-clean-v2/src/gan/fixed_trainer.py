#!/usr/bin/env python3
"""
修复的GAN训练器 - 解决维度不匹配和NaN输出问题
主要修复：
1. 使用维度修复的模型
2. 改进的损失计算和数值稳定性
3. 更好的训练流程控制
4. 统一的数据处理逻辑
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple, Optional
import logging
from .fixed_models import DimensionFixedGenerator, DimensionFixedDiscriminator


class FixedGANConfig:
    """修复的GAN配置"""
    def __init__(self):
        self.noise_dim = 128
        self.embedding_dim = 16
        self.hidden_dim = 256
        self.batch_size = 32
        self.epochs = 100
        
        # 学习率设置
        self.g_lr = 0.0002
        self.d_lr = 0.0001  # 判别器学习率稍低
        
        # 训练步数比例
        self.d_steps = 1
        self.g_steps = 2  # 生成器多训练几步
        
        # 损失权重
        self.lambda_adv = 1.0
        self.lambda_ctr = 0.5
        self.lambda_diversity = 0.1
        
        # 温度参数
        self.initial_temperature = 2.0
        self.min_temperature = 0.5
        self.temperature_decay = 0.995
        
        # 数值稳定性
        self.eps = 1e-8
        self.gradient_clip = 1.0


class FixedGANTrainer:
    """修复的GAN训练器"""
    
    def __init__(self, generator: DimensionFixedGenerator, discriminator: DimensionFixedDiscriminator,
                 config: FixedGANConfig, device: torch.device):
        self.generator = generator.to(device)
        self.discriminator = discriminator.to(device)
        self.config = config
        self.device = device
        
        # 优化器
        self.g_optimizer = torch.optim.Adam(
            self.generator.parameters(), 
            lr=config.g_lr, 
            betas=(0.5, 0.999),
            eps=config.eps
        )
        
        self.d_optimizer = torch.optim.Adam(
            self.discriminator.parameters(), 
            lr=config.d_lr, 
            betas=(0.5, 0.999),
            eps=config.eps
        )
        
        # 学习率调度器
        self.g_scheduler = torch.optim.lr_scheduler.ExponentialLR(self.g_optimizer, gamma=0.99)
        self.d_scheduler = torch.optim.lr_scheduler.ExponentialLR(self.d_optimizer, gamma=0.99)
        
        # 温度参数
        self.current_temperature = config.initial_temperature
        
        self.logger = logging.getLogger(__name__)
    
    def train_discriminator(self, real_batch: Tuple) -> Dict[str, float]:
        """训练判别器"""
        self.d_optimizer.zero_grad()
        
        real_numeric, real_categorical, real_ctr_labels = real_batch
        real_numeric = real_numeric.to(self.device) if real_numeric is not None else None
        real_categorical = real_categorical.to(self.device) if real_categorical is not None else None
        real_ctr_labels = real_ctr_labels.to(self.device)
        
        batch_size = real_ctr_labels.size(0)
        
        # 1. 训练真实数据
        try:
            real_rf_score, real_ctr_pred, _ = self.discriminator(
                numeric_data=real_numeric,
                categorical_data=real_categorical
            )
            
            # 真实数据的目标
            real_rf_target = torch.ones_like(real_rf_score)
            real_ctr_target = real_ctr_labels.float().unsqueeze(-1) if real_ctr_labels.dim() == 1 else real_ctr_labels.float()
            
            # 损失计算 - 添加数值稳定性
            d_real_rf_loss = F.binary_cross_entropy(real_rf_score, real_rf_target)
            d_real_ctr_loss = F.binary_cross_entropy(real_ctr_pred, real_ctr_target)
            
        except Exception as e:
            self.logger.error(f"Real data discriminator forward failed: {e}")
            return {"d_loss": float('inf'), "d_real_rf_loss": float('inf'), "d_real_ctr_loss": float('inf')}
        
        # 2. 训练生成数据
        try:
            # 生成假数据
            noise = torch.randn(batch_size, self.config.noise_dim, device=self.device)
            
            with torch.no_grad():
                fake_output = self.generator(noise, temperature=self.current_temperature, hard=False)
            
            fake_rf_score, fake_ctr_pred, _ = self.discriminator(
                numeric_data=fake_output['numeric'],
                categorical_embeddings=fake_output['categorical_embeddings']
            )
            
            # 假数据的目标
            fake_rf_target = torch.zeros_like(fake_rf_score)
            
            # 损失计算
            d_fake_rf_loss = F.binary_cross_entropy(fake_rf_score, fake_rf_target)
            
        except Exception as e:
            self.logger.error(f"Fake data discriminator forward failed: {e}")
            return {"d_loss": float('inf'), "d_fake_rf_loss": float('inf')}
        
        # 总损失
        d_loss = d_real_rf_loss + d_fake_rf_loss + d_real_ctr_loss
        
        # 反向传播
        try:
            d_loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(self.discriminator.parameters(), self.config.gradient_clip)
            
            self.d_optimizer.step()
            
        except Exception as e:
            self.logger.error(f"Discriminator backward failed: {e}")
            return {"d_loss": float('inf')}
        
        return {
            "d_loss": d_loss.item(),
            "d_real_rf_loss": d_real_rf_loss.item(),
            "d_fake_rf_loss": d_fake_rf_loss.item(),
            "d_real_ctr_loss": d_real_ctr_loss.item(),
            "d_real_rf_acc": ((real_rf_score > 0.5).float().mean()).item(),
            "d_fake_rf_acc": ((fake_rf_score < 0.5).float().mean()).item()
        }
    
    def train_generator(self, real_batch: Tuple) -> Dict[str, float]:
        """训练生成器"""
        self.g_optimizer.zero_grad()
        
        _, _, real_ctr_labels = real_batch
        real_ctr_labels = real_ctr_labels.to(self.device)
        batch_size = real_ctr_labels.size(0)
        
        try:
            # 生成假数据
            noise = torch.randn(batch_size, self.config.noise_dim, device=self.device)
            fake_output = self.generator(noise, temperature=self.current_temperature, hard=False)
            
            # 通过判别器
            fake_rf_score, fake_ctr_pred, _ = self.discriminator(
                numeric_data=fake_output['numeric'],
                categorical_embeddings=fake_output['categorical_embeddings']
            )
            
            # 生成器损失
            # 1. 对抗损失 (欺骗判别器)
            g_adv_loss = F.binary_cross_entropy(fake_rf_score, torch.ones_like(fake_rf_score))
            
            # 2. CTR一致性损失 - 使用真实CTR分布
            real_ctr_rate = real_ctr_labels.float().mean().item()
            fake_ctr_labels = torch.bernoulli(torch.full((batch_size,), real_ctr_rate, device=self.device))
            fake_ctr_target = fake_ctr_labels.float().unsqueeze(-1) if fake_ctr_labels.dim() == 1 else fake_ctr_labels.float()
            
            g_ctr_loss = F.binary_cross_entropy(fake_ctr_pred, fake_ctr_target)
            
            # 3. 多样性损失
            g_diversity_loss = 0
            if fake_output['numeric'] is not None:
                # 确保数值特征有足够的方差
                feature_stds = torch.std(fake_output['numeric'], dim=0)
                g_diversity_loss = torch.mean(F.relu(0.1 - feature_stds))
            
            # 总损失
            g_loss = (self.config.lambda_adv * g_adv_loss + 
                     self.config.lambda_ctr * g_ctr_loss + 
                     self.config.lambda_diversity * g_diversity_loss)
            
            # 反向传播
            g_loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(self.generator.parameters(), self.config.gradient_clip)
            
            self.g_optimizer.step()
            
        except Exception as e:
            self.logger.error(f"Generator training failed: {e}")
            return {"g_loss": float('inf')}
        
        return {
            "g_loss": g_loss.item(),
            "g_adv_loss": g_adv_loss.item(),
            "g_ctr_loss": g_ctr_loss.item(),
            "g_diversity_loss": g_diversity_loss.item() if isinstance(g_diversity_loss, torch.Tensor) else g_diversity_loss,
            "g_rf_score_mean": fake_rf_score.mean().item()
        }
    
    def train_step(self, real_batch: Tuple) -> Dict[str, float]:
        """单步训练"""
        # 训练判别器
        d_metrics = {}
        for _ in range(self.config.d_steps):
            d_step_metrics = self.train_discriminator(real_batch)
            for k, v in d_step_metrics.items():
                d_metrics[k] = d_metrics.get(k, 0) + v / self.config.d_steps
        
        # 训练生成器
        g_metrics = {}
        for _ in range(self.config.g_steps):
            g_step_metrics = self.train_generator(real_batch)
            for k, v in g_step_metrics.items():
                g_metrics[k] = g_metrics.get(k, 0) + v / self.config.g_steps
        
        # 更新温度
        self.current_temperature = max(
            self.config.min_temperature,
            self.current_temperature * self.config.temperature_decay
        )
        
        # 合并指标
        all_metrics = {**d_metrics, **g_metrics}
        all_metrics["temperature"] = self.current_temperature
        
        return all_metrics
    
    def update_learning_rates(self):
        """更新学习率"""
        self.g_scheduler.step()
        self.d_scheduler.step()

    def train_epoch(self, dataloader):
        """训练一个epoch"""
        epoch_metrics = {
            'd_loss': 0.0,
            'g_loss': 0.0,
            'd_real_rf_loss': 0.0,
            'd_fake_rf_loss': 0.0,
            'd_real_ctr_loss': 0.0,
            'g_adv_loss': 0.0,
            'g_ctr_loss': 0.0,
            'g_diversity_loss': 0.0,
            'd_real_rf_acc': 0.0,
            'd_fake_rf_acc': 0.0,
            'g_rf_score_mean': 0.0,
            'temperature': 0.0
        }

        num_batches = 0

        for batch_idx, batch in enumerate(dataloader):
            # 训练一步
            step_metrics = self.train_step(batch)

            # 累积指标
            for key, value in step_metrics.items():
                if key in epoch_metrics:
                    epoch_metrics[key] += value

            num_batches += 1

            # 可选：打印进度
            if batch_idx % 100 == 0:
                self.logger.info(f"Batch {batch_idx}/{len(dataloader)}: d_loss={step_metrics['d_loss']:.4f}, g_loss={step_metrics['g_loss']:.4f}")

        # 计算平均值
        if num_batches > 0:
            for key in epoch_metrics:
                epoch_metrics[key] /= num_batches

        return epoch_metrics
