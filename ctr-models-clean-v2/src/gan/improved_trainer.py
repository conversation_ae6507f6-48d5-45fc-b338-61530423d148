#!/usr/bin/env python3
"""
改进的GAN训练器 - 解决循环CTR依赖问题
主要改进：
1. 使用条件生成器，CTR标签作为输入而非输出
2. 修复特征-CTR关系学习
3. 更强的判别器架构
4. 改进的损失函数设计
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import numpy as np
import logging
from typing import Dict, List, Tuple, Optional
import wandb

from .models import ImprovedConditionalGenerator, ImprovedDiscriminator


class ImprovedGANConfig:
    """改进的GAN配置"""
    def __init__(self):
        # 模型参数
        self.noise_dim = 128
        self.embedding_dim = 16
        self.ctr_embedding_dim = 8
        self.discriminator_hidden_dim = 256
        
        # 训练参数
        self.batch_size = 512
        self.epochs = 50
        self.generator_lr = 1e-4  # 降低生成器学习率
        self.discriminator_lr = 2e-4  # 提高判别器学习率
        
        # 损失权重
        self.lambda_adversarial = 1.0
        self.lambda_ctr_consistency = 2.0  # 增强CTR一致性
        self.lambda_feature_matching = 0.5
        self.lambda_diversity = 0.1
        
        # 训练策略
        self.d_steps = 2  # 每轮训练判别器2次
        self.g_steps = 1  # 每轮训练生成器1次
        
        # 正则化
        self.max_grad_norm = 1.0
        self.label_smoothing = 0.1
        
        # 温度调度
        self.initial_temperature = 2.0
        self.min_temperature = 0.5
        self.temperature_decay = 0.995
        
        # 日志
        self.log_interval = 100
        self.save_interval = 5


class ImprovedGANTrainer:
    """
    改进的GAN训练器 - 解决循环CTR依赖
    """
    
    def __init__(self, generator: ImprovedConditionalGenerator, 
                 discriminator: ImprovedDiscriminator, config: ImprovedGANConfig):
        self.generator = generator
        self.discriminator = discriminator
        self.config = config
        
        # 设备
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.generator.to(self.device)
        self.discriminator.to(self.device)
        
        # 优化器
        self.g_optimizer = optim.Adam(
            self.generator.parameters(), 
            lr=config.generator_lr, 
            betas=(0.5, 0.999)
        )
        self.d_optimizer = optim.Adam(
            self.discriminator.parameters(), 
            lr=config.discriminator_lr, 
            betas=(0.5, 0.999)
        )
        
        # 学习率调度器
        self.g_scheduler = optim.lr_scheduler.ExponentialLR(self.g_optimizer, gamma=0.99)
        self.d_scheduler = optim.lr_scheduler.ExponentialLR(self.d_optimizer, gamma=0.99)
        
        # 训练状态
        self.current_epoch = 0
        self.current_temperature = config.initial_temperature
        self.total_steps = 0
        
        # 损失历史
        self.d_loss_history = []
        self.g_loss_history = []
        self.ctr_accuracy_history = []
        
        self.logger = logging.getLogger(__name__)
        
    def sample_ctr_labels(self, batch_size: int, real_ctr_rate: float) -> torch.Tensor:
        """
        采样CTR标签 - 保持真实CTR分布
        """
        # 使用真实CTR率进行采样
        ctr_labels = torch.bernoulli(torch.full((batch_size,), real_ctr_rate))
        return ctr_labels.to(self.device)
    
    def train_discriminator(self, real_batch: Tuple) -> Dict[str, float]:
        """
        训练判别器 - 改进版本
        """
        self.d_optimizer.zero_grad()

        real_numeric, real_categorical, real_ctr_labels = real_batch
        real_numeric = real_numeric.to(self.device) if real_numeric is not None else None
        real_categorical = real_categorical.to(self.device) if real_categorical is not None else None
        real_ctr_labels = real_ctr_labels.to(self.device)

        # 安全检查：确保categorical indices在有效范围内
        if real_categorical is not None:
            # 将负值和过大值截断到有效范围
            real_categorical = torch.clamp(real_categorical, min=0, max=99999)  # 设置一个合理的上限

        batch_size = real_ctr_labels.size(0)
        
        # 真实数据通过判别器
        try:
            real_rf_score, real_ctr_pred, real_features = self.discriminator(
                numeric_data=real_numeric,
                categorical_data=real_categorical
            )
        except RuntimeError as e:
            self.logger.error(f"Error in discriminator forward pass (real data): {e}")
            if "CUDA" in str(e):
                self.logger.error(f"Real data shapes: numeric={real_numeric.shape if real_numeric is not None else None}, "
                                f"categorical={real_categorical.shape if real_categorical is not None else None}")
                if real_categorical is not None:
                    self.logger.error(f"Categorical data range: min={real_categorical.min()}, max={real_categorical.max()}")
            raise
        
        # 生成假数据 - 关键改进：使用真实CTR分布
        real_ctr_rate = real_ctr_labels.float().mean().item()
        fake_ctr_labels = self.sample_ctr_labels(batch_size, real_ctr_rate)
        
        noise = torch.randn(batch_size, self.config.noise_dim, device=self.device)
        with torch.no_grad():
            fake_output = self.generator(
                noise=noise, 
                ctr_labels=fake_ctr_labels,  # 条件输入！
                temperature=self.current_temperature, 
                hard=True
            )
        
        # 假数据通过判别器
        try:
            fake_rf_score, fake_ctr_pred, fake_features = self.discriminator(
                numeric_data=fake_output['numeric'],
                categorical_embeddings=fake_output['categorical_embeddings']
            )
        except RuntimeError as e:
            self.logger.error(f"Error in discriminator forward pass (fake data): {e}")
            if "CUDA" in str(e):
                self.logger.error(f"Fake data shapes: numeric={fake_output['numeric'].shape if fake_output['numeric'] is not None else None}")
                if fake_output['categorical_embeddings'] is not None:
                    emb_shapes = [emb.shape if emb is not None else None for emb in fake_output['categorical_embeddings']]
                    self.logger.error(f"Categorical embeddings shapes: {emb_shapes}")
            raise
        
        # 判别器损失
        # 1. 真假判别损失 (带标签平滑)
        real_target = torch.ones_like(real_rf_score) * (1.0 - self.config.label_smoothing)
        fake_target = torch.zeros_like(fake_rf_score) + self.config.label_smoothing
        
        d_real_loss = F.binary_cross_entropy(real_rf_score, real_target)
        d_fake_loss = F.binary_cross_entropy(fake_rf_score, fake_target)
        d_adv_loss = (d_real_loss + d_fake_loss) / 2
        
        # 2. CTR预测损失 (只在真实数据上)
        d_ctr_loss = F.binary_cross_entropy(real_ctr_pred.squeeze(), real_ctr_labels.float())
        
        # 3. 特征匹配正则化 - 简化版本避免CUDA错误
        feature_reg_value = 0.0
        try:
            if (real_features.shape == fake_features.shape and
                real_features.numel() > 0 and fake_features.numel() > 0 and
                real_features.shape[1] == fake_features.shape[1]):

                # 使用更安全的计算方式
                real_mean = real_features.mean(dim=0, keepdim=False)
                fake_mean = fake_features.mean(dim=0, keepdim=False)

                if real_mean.shape == fake_mean.shape:
                    diff = real_mean - fake_mean
                    feature_reg_value = torch.mean(diff * diff).item()
                else:
                    self.logger.warning(f"Feature mean shape mismatch: real_mean={real_mean.shape}, fake_mean={fake_mean.shape}")
            else:
                # 维度不匹配时跳过特征匹配
                self.logger.debug(f"Skipping feature matching due to shape mismatch: real={real_features.shape}, fake={fake_features.shape}")
        except Exception as e:
            self.logger.error(f"Error in feature matching computation: {e}")
            feature_reg_value = 0.0
        
        # 总损失
        d_loss = (self.config.lambda_adversarial * d_adv_loss +
                 self.config.lambda_ctr_consistency * d_ctr_loss +
                 0.1 * feature_reg)
        
        # 反向传播
        d_loss.backward()
        
        # 梯度裁剪
        if self.config.max_grad_norm > 0:
            torch.nn.utils.clip_grad_norm_(self.discriminator.parameters(), self.config.max_grad_norm)
        
        self.d_optimizer.step()
        
        # 计算CTR预测准确率
        ctr_pred_binary = (real_ctr_pred.squeeze() > 0.5).float()
        ctr_accuracy = (ctr_pred_binary == real_ctr_labels.float()).float().mean().item()
        
        return {
            'd_loss': d_loss.item(),
            'd_adv_loss': d_adv_loss.item(),
            'd_ctr_loss': d_ctr_loss.item(),
            'real_rf_score': real_rf_score.mean().item(),
            'fake_rf_score': fake_rf_score.mean().item(),
            'ctr_accuracy': ctr_accuracy,
            'feature_reg': feature_reg.item() if isinstance(feature_reg, torch.Tensor) else feature_reg
        }
    
    def train_generator(self, real_batch: Tuple) -> Dict[str, float]:
        """
        训练生成器 - 改进版本
        """
        self.g_optimizer.zero_grad()
        
        _, _, real_ctr_labels = real_batch
        real_ctr_labels = real_ctr_labels.to(self.device)
        batch_size = real_ctr_labels.size(0)
        
        # 采样CTR标签 - 保持真实分布
        real_ctr_rate = real_ctr_labels.float().mean().item()
        fake_ctr_labels = self.sample_ctr_labels(batch_size, real_ctr_rate)
        
        # 生成假数据
        noise = torch.randn(batch_size, self.config.noise_dim, device=self.device)
        fake_output = self.generator(
            noise=noise, 
            ctr_labels=fake_ctr_labels,  # 条件输入！
            temperature=self.current_temperature, 
            hard=False  # 软采样用于梯度
        )
        
        # 通过判别器
        fake_rf_score, fake_ctr_pred, fake_features = self.discriminator(
            numeric_data=fake_output['numeric'],
            categorical_embeddings=fake_output['categorical_embeddings']
        )
        
        # 生成器损失
        # 1. 对抗损失 (欺骗判别器)
        g_adv_loss = F.binary_cross_entropy(fake_rf_score, torch.ones_like(fake_rf_score))
        
        # 2. CTR一致性损失 - 关键改进！
        # 生成的数据应该与输入的CTR标签一致
        g_ctr_loss = F.binary_cross_entropy(fake_ctr_pred.squeeze(), fake_ctr_labels.float())
        
        # 3. 特征多样性损失
        g_diversity_loss = 0
        if fake_output['numeric'] is not None:
            # 确保数值特征有足够的方差
            feature_stds = torch.std(fake_output['numeric'], dim=0)
            g_diversity_loss = torch.mean(F.relu(0.05 - feature_stds))  # 更强的多样性要求
        
        # 总损失
        g_loss = (self.config.lambda_adversarial * g_adv_loss + 
                 self.config.lambda_ctr_consistency * g_ctr_loss +
                 self.config.lambda_diversity * g_diversity_loss)
        
        # 反向传播
        g_loss.backward()
        
        # 梯度裁剪
        if self.config.max_grad_norm > 0:
            torch.nn.utils.clip_grad_norm_(self.generator.parameters(), self.config.max_grad_norm)
        
        self.g_optimizer.step()
        
        return {
            'g_loss': g_loss.item(),
            'g_adv_loss': g_adv_loss.item(),
            'g_ctr_loss': g_ctr_loss.item(),
            'g_diversity_loss': g_diversity_loss.item() if isinstance(g_diversity_loss, torch.Tensor) else g_diversity_loss,
            'fake_rf_score_g': fake_rf_score.mean().item()
        }
    
    def train_step(self, real_batch: Tuple) -> Dict[str, float]:
        """单步训练"""
        # 训练判别器
        d_metrics = {}
        for _ in range(self.config.d_steps):
            d_step_metrics = self.train_discriminator(real_batch)
            for k, v in d_step_metrics.items():
                d_metrics[k] = d_metrics.get(k, 0) + v / self.config.d_steps
        
        # 训练生成器
        g_metrics = {}
        for _ in range(self.config.g_steps):
            g_step_metrics = self.train_generator(real_batch)
            for k, v in g_step_metrics.items():
                g_metrics[k] = g_metrics.get(k, 0) + v / self.config.g_steps
        
        # 更新温度
        self.current_temperature = max(
            self.config.min_temperature,
            self.current_temperature * self.config.temperature_decay
        )
        
        # 记录历史
        self.d_loss_history.append(d_metrics['d_loss'])
        self.g_loss_history.append(g_metrics['g_loss'])
        self.ctr_accuracy_history.append(d_metrics['ctr_accuracy'])
        
        # 合并指标
        metrics = {**d_metrics, **g_metrics, 'temperature': self.current_temperature}
        
        self.total_steps += 1
        return metrics
    
    def train_epoch(self, dataloader: DataLoader) -> Dict[str, float]:
        """训练一个epoch"""
        self.generator.train()
        self.discriminator.train()
        
        epoch_metrics = {}
        num_batches = 0
        
        for batch_idx, batch in enumerate(dataloader):
            metrics = self.train_step(batch)
            
            # 累积指标
            for k, v in metrics.items():
                epoch_metrics[k] = epoch_metrics.get(k, 0) + v
            num_batches += 1
            
            # 日志记录
            if batch_idx % self.config.log_interval == 0:
                self.logger.info(
                    f"Epoch {self.current_epoch}, Batch {batch_idx}: "
                    f"D_loss={metrics['d_loss']:.4f}, G_loss={metrics['g_loss']:.4f}, "
                    f"CTR_acc={metrics['ctr_accuracy']:.4f}, Temp={metrics['temperature']:.3f}"
                )
                
                # 记录到wandb
                wandb.log({
                    'batch_d_loss': metrics['d_loss'],
                    'batch_g_loss': metrics['g_loss'],
                    'batch_ctr_accuracy': metrics['ctr_accuracy'],
                    'temperature': metrics['temperature'],
                    'step': self.total_steps
                })
        
        # 平均指标
        for k in epoch_metrics:
            epoch_metrics[k] /= num_batches
        
        # 更新学习率
        self.g_scheduler.step()
        self.d_scheduler.step()
        
        self.current_epoch += 1
        return epoch_metrics

    def save_checkpoint(self, path: str, epoch: int, metrics: Dict):
        """保存检查点"""
        torch.save({
            'epoch': epoch,
            'generator_state_dict': self.generator.state_dict(),
            'discriminator_state_dict': self.discriminator.state_dict(),
            'g_optimizer_state_dict': self.g_optimizer.state_dict(),
            'd_optimizer_state_dict': self.d_optimizer.state_dict(),
            'g_scheduler_state_dict': self.g_scheduler.state_dict(),
            'd_scheduler_state_dict': self.d_scheduler.state_dict(),
            'config': self.config,
            'current_temperature': self.current_temperature,
            'total_steps': self.total_steps,
            'metrics': metrics
        }, path)
        self.logger.info(f"Checkpoint saved to {path}")

    def load_checkpoint(self, path: str):
        """加载检查点"""
        checkpoint = torch.load(path, map_location=self.device)

        self.generator.load_state_dict(checkpoint['generator_state_dict'])
        self.discriminator.load_state_dict(checkpoint['discriminator_state_dict'])
        self.g_optimizer.load_state_dict(checkpoint['g_optimizer_state_dict'])
        self.d_optimizer.load_state_dict(checkpoint['d_optimizer_state_dict'])

        if 'g_scheduler_state_dict' in checkpoint:
            self.g_scheduler.load_state_dict(checkpoint['g_scheduler_state_dict'])
        if 'd_scheduler_state_dict' in checkpoint:
            self.d_scheduler.load_state_dict(checkpoint['d_scheduler_state_dict'])

        self.current_epoch = checkpoint['epoch']
        self.current_temperature = checkpoint.get('current_temperature', self.config.initial_temperature)
        self.total_steps = checkpoint.get('total_steps', 0)

        self.logger.info(f"Checkpoint loaded from {path}, epoch {self.current_epoch}")
        return checkpoint.get('metrics', {})
