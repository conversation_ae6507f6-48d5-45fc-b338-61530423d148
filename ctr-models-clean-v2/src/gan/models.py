import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import List, Dict, Tuple
import math


class FixedNoiseEncoder(nn.Module):
    """
    修复的噪声编码器 - 确保输出维度正确
    """
    
    def __init__(self, noise_dim: int = 128, encoded_dim: int = 512):
        super(FixedNoiseEncoder, self).__init__()
        self.noise_dim = noise_dim
        self.encoded_dim = encoded_dim
        
        # 简化架构，确保维度正确
        self.encoder = nn.Sequential(
            nn.Linear(noise_dim, 256),
            nn.LayerNorm(256),
            nn.LeakyReLU(0.2),
            nn.Dropout(0.2),
            
            nn.Linear(256, 384),
            nn.<PERSON><PERSON>(384),
            nn.LeakyReLU(0.2),
            nn.Dropout(0.1),
            
            nn.Linear(384, encoded_dim),  # 直接输出到目标维度
            nn.LayerNorm(encoded_dim),
            nn.LeakyReLU(0.2)
        )
        
        # 额外的噪声生成器增加多样性
        self.noise_generator = nn.Sequential(
            nn.Linear(noise_dim, 128),
            nn.<PERSON>ky<PERSON>e<PERSON>(0.2),
            nn.<PERSON>ar(128, encoded_dim),
            nn.Tanh()
        )
        
    def forward(self, noise, stage=None):
        # 主要编码
        main_encoded = self.encoder(noise)
        
        # 额外噪声增强多样性
        noise_enhanced = noise + 0.1 * torch.randn_like(noise)
        extra_noise = self.noise_generator(noise_enhanced)
        
        # 组合输出
        final_encoded = main_encoded + 0.2 * extra_noise
        
        # 添加随机噪声
        final_encoded = final_encoded + 0.05 * torch.randn_like(final_encoded)
        
        return final_encoded


class FixedNumericGenerator(nn.Module):
    """
    修复的数值特征生成器 - 解决标准差为0的问题
    """
    
    def __init__(self, encoded_dim: int, num_features: int):
        super(FixedNumericGenerator, self).__init__()
        self.num_features = num_features
        
        if num_features > 0:
            # 使用更简单但更稳定的架构
            self.main_generator = nn.Sequential(
                nn.Linear(encoded_dim, 256),
                nn.LayerNorm(256),
                nn.LeakyReLU(0.2),
                nn.Dropout(0.3),
                
                nn.Linear(256, 128),
                nn.LayerNorm(128),
                nn.LeakyReLU(0.2),
                nn.Dropout(0.2),
                
                nn.Linear(128, 64),
                nn.LayerNorm(64),
                nn.LeakyReLU(0.2),
                
                nn.Linear(64, num_features)
                # 移除Tanh，使用更自由的输出
            )
            
            # 添加独立的噪声生成器确保多样性
            self.diversity_generators = nn.ModuleList([
                nn.Sequential(
                    nn.Linear(encoded_dim, 32),
                    nn.LeakyReLU(0.2),
                    nn.Linear(32, 1),
                    nn.Tanh()
                ) for _ in range(num_features)
            ])
            
            # 可学习的缩放和偏移参数
            self.scales = nn.Parameter(torch.ones(num_features))
            self.biases = nn.Parameter(torch.zeros(num_features))
            
        else:
            self.main_generator = None
            self.diversity_generators = None
            
    def forward(self, encoded_noise):
        if self.main_generator is None:
            return None
            
        # 主要输出
        main_output = self.main_generator(encoded_noise)
        
        # 多样性输出
        diversity_outputs = []
        for i, div_gen in enumerate(self.diversity_generators):
            # 为每个特征添加独立的噪声
            feature_noise = encoded_noise + 0.3 * torch.randn_like(encoded_noise)
            div_out = div_gen(feature_noise)
            diversity_outputs.append(div_out)
        
        diversity_output = torch.cat(diversity_outputs, dim=-1)
        
        # 组合输出：主输出 + 多样性输出
        combined = main_output + 0.3 * diversity_output

        # 应用可学习的缩放和偏移
        scaled_output = combined * self.scales + self.biases

        # 减少随机噪声，避免过度随机化
        final_output = scaled_output + 0.05 * torch.randn_like(scaled_output)

        # 使用更宽松的激活函数，允许更大的范围
        return 2.0 * torch.tanh(final_output)  # 扩展到[-2, 2]范围


class FixedCategoricalGenerator(nn.Module):
    """
    修复维度问题的类别特征生成器
    """
    
    def __init__(self, encoded_dim: int, vocab_sizes: List[int], embedding_dim: int = 16):
        super(FixedCategoricalGenerator, self).__init__()
        self.vocab_sizes = vocab_sizes
        self.embedding_dim = embedding_dim
        self.num_categories = len(vocab_sizes)
        
        if self.num_categories > 0:
            # 修复：确保维度匹配
            self.groups = self._create_feature_groups(vocab_sizes)
            
            # 计算每组需要的输入维度
            self.group_input_dims = []
            total_groups = len(self.groups)
            
            # 平均分配编码维度
            base_dim = encoded_dim // total_groups
            remaining_dim = encoded_dim % total_groups
            
            for i in range(total_groups):
                # 前面几组多分配一些维度（如果有剩余）
                group_dim = base_dim + (1 if i < remaining_dim else 0)
                self.group_input_dims.append(group_dim)
            
            print(f"DEBUG: encoded_dim={encoded_dim}, groups={len(self.groups)}")
            print(f"DEBUG: group_input_dims={self.group_input_dims}")
            
            # 为每组创建独立的生成器
            self.group_generators = nn.ModuleList()
            self.group_embeddings = nn.ModuleList()
            
            for group_idx, group_indices in enumerate(self.groups):
                group_vocab_sizes = [vocab_sizes[i] for i in group_indices]
                group_input_dim = self.group_input_dims[group_idx]
                
                # 共享编码器 - 使用正确的输入维度
                shared_encoder = nn.Sequential(
                    nn.Linear(group_input_dim, 128),
                    nn.LayerNorm(128),
                    nn.LeakyReLU(0.2),
                    nn.Dropout(0.2),
                    nn.Linear(128, 96),
                    nn.LayerNorm(96),
                    nn.LeakyReLU(0.2),
                    nn.Dropout(0.1)
                )
                
                # 分类头
                category_heads = nn.ModuleList([
                    nn.Sequential(
                        nn.Linear(96, 64),
                        nn.LeakyReLU(0.2),
                        nn.Dropout(0.1),
                        nn.Linear(64, vocab_size)
                    ) for vocab_size in group_vocab_sizes
                ])
                
                # Embedding层
                embeddings = nn.ModuleList([
                    nn.Embedding(vocab_size, embedding_dim) 
                    for vocab_size in group_vocab_sizes
                ])
                
                self.group_generators.append(nn.ModuleDict({
                    'shared_encoder': shared_encoder,
                    'category_heads': category_heads
                }))
                self.group_embeddings.append(embeddings)
                
                # 初始化embedding权重
                for embedding in embeddings:
                    nn.init.normal_(embedding.weight, 0.0, 0.02)
        else:
            self.groups = []
            self.group_generators = None
            self.group_embeddings = None
    
    def _create_feature_groups(self, vocab_sizes):
        """根据vocab_size大小创建特征分组"""
        # 更细粒度的分组策略
        small_features = [i for i, size in enumerate(vocab_sizes) if size <= 50]
        medium_features = [i for i, size in enumerate(vocab_sizes) if 50 < size <= 1000]
        large_features = [i for i, size in enumerate(vocab_sizes) if 1000 < size <= 5000]
        xlarge_features = [i for i, size in enumerate(vocab_sizes) if size > 5000]
        
        groups = []
        
        # 小特征可以放在一组
        if small_features:
            groups.append(small_features)
        
        # 中等特征分成小组
        if medium_features:
            for i in range(0, len(medium_features), 6):
                groups.append(medium_features[i:i+6])
        
        # 大特征分成更小组
        if large_features:
            for i in range(0, len(large_features), 3):
                groups.append(large_features[i:i+3])
                
        # 超大特征每个单独一组
        if xlarge_features:
            for feat in xlarge_features:
                groups.append([feat])
                
        return groups
            
    def forward(self, encoded_noise, temperature=1.0, hard=False):
        if self.group_generators is None:
            return None, None, None
            
        # 修复：按照预计算的维度分配噪声
        noise_splits = []
        start_idx = 0
        for group_dim in self.group_input_dims:
            end_idx = start_idx + group_dim
            noise_splits.append(encoded_noise[:, start_idx:end_idx])
            start_idx = end_idx
        
        all_logits = [None] * self.num_categories
        all_probs = [None] * self.num_categories  
        all_embeddings = [None] * self.num_categories
        
        for group_idx, (group_indices, group_noise) in enumerate(zip(self.groups, noise_splits)):
            generator = self.group_generators[group_idx]
            embeddings = self.group_embeddings[group_idx]
            
            # 为每组添加独立噪声
            enhanced_noise = group_noise + 0.1 * torch.randn_like(group_noise)
            
            # 通过共享编码器
            shared_features = generator['shared_encoder'](enhanced_noise)
            
            # 为组内每个特征生成
            for local_idx, global_idx in enumerate(group_indices):
                head = generator['category_heads'][local_idx]
                embedding_layer = embeddings[local_idx]
                
                # 为每个特征添加独立噪声
                feature_noise = shared_features + 0.1 * torch.randn_like(shared_features)
                
                # 生成logits
                logits = head(feature_noise)
                all_logits[global_idx] = logits
                
                # 改进的Gumbel-Softmax采样
                effective_temp = max(temperature, 0.3)  # 提高最小温度
                
                # 添加额外的随机性到logits
                if not hard:
                    logits = logits + 0.1 * torch.randn_like(logits)
                
                probs = F.gumbel_softmax(logits, tau=effective_temp, hard=hard, dim=-1)
                all_probs[global_idx] = probs
                
                # 通过embedding获取表示
                if hard:
                    indices = torch.argmax(probs, dim=-1)
                    emb = embedding_layer(indices)
                else:
                    # 改进的soft embedding计算
                    emb = torch.matmul(probs, embedding_layer.weight)
                    # 添加噪声增强多样性
                    emb = emb + 0.02 * torch.randn_like(emb)
                    
                all_embeddings[global_idx] = emb
                
        return all_logits, all_probs, all_embeddings


class FixedImprovedGenerator(nn.Module):
    """
    修复维度问题的改进生成器
    """
    
    def __init__(self, noise_dim: int, numeric_features: List[str], 
                 categorical_features: List[str], vocab_sizes: List[int],
                 embedding_dim: int = 16):
        super(FixedImprovedGenerator, self).__init__()
        
        self.noise_dim = noise_dim
        self.numeric_features = numeric_features
        self.categorical_features = categorical_features
        self.num_numeric = len(numeric_features)
        self.num_categorical = len(categorical_features)
        
        # 修复的噪声编码器 - 简化版本确保维度正确
        self.noise_encoder = FixedNoiseEncoder(noise_dim, encoded_dim=512)
        
        # 重新设计维度分配 - 确保总和为512
        if self.num_numeric > 0 and self.num_categorical > 0:
            # 给数值特征固定256维，类别特征256维
            numeric_dim = 256  
            categorical_dim = 256  
        elif self.num_numeric > 0:
            numeric_dim = 512
            categorical_dim = 0
        else:
            numeric_dim = 0
            categorical_dim = 512
        
        self.numeric_dim = numeric_dim
        self.categorical_dim = categorical_dim
        
        print(f"DEBUG: Generator dimensions - numeric: {numeric_dim}, categorical: {categorical_dim}")
        
        # 特征生成器
        self.numeric_generator = FixedNumericGenerator(
            encoded_dim=numeric_dim, 
            num_features=self.num_numeric
        ) if numeric_dim > 0 else None
        
        self.categorical_generator = FixedCategoricalGenerator(
            encoded_dim=categorical_dim,
            vocab_sizes=vocab_sizes,
            embedding_dim=embedding_dim
        ) if categorical_dim > 0 else None
        
    def forward(self, noise, temperature=1.0, hard=False):
        # 编码噪声
        encoded_noise = self.noise_encoder(noise)
        
        # 确保编码后的噪声维度正确
        assert encoded_noise.size(1) == 512, f"Expected 512 dims, got {encoded_noise.size(1)}"
        
        # 分配给不同的生成器
        numeric_output = None
        cat_logits, cat_probs, cat_embeddings = None, None, None
        
        if self.numeric_dim > 0 and self.numeric_generator is not None:
            numeric_noise = encoded_noise[:, :self.numeric_dim]
            numeric_output = self.numeric_generator(numeric_noise)
            
        if self.categorical_dim > 0 and self.categorical_generator is not None:
            categorical_noise = encoded_noise[:, -self.categorical_dim:]
            cat_logits, cat_probs, cat_embeddings = self.categorical_generator(
                categorical_noise, temperature, hard
            )
        
        return {
            'numeric': numeric_output,
            'categorical_logits': cat_logits,
            'categorical_probs': cat_probs,
            'categorical_embeddings': cat_embeddings
        }


class ImprovedConditionalGenerator(nn.Module):
    """
    改进的条件生成器 - 解决循环CTR依赖问题
    Generator接收CTR标签作为条件输入，学习特征-CTR关系
    """

    def __init__(self, noise_dim: int, numeric_features: List[str],
                 categorical_features: List[str], vocab_sizes: List[int],
                 embedding_dim: int = 16, ctr_embedding_dim: int = 8):
        super(ImprovedConditionalGenerator, self).__init__()

        self.noise_dim = noise_dim
        self.numeric_features = numeric_features
        self.categorical_features = categorical_features
        self.num_numeric = len(numeric_features)
        self.num_categorical = len(categorical_features)
        self.ctr_embedding_dim = ctr_embedding_dim

        # CTR标签嵌入层 - 关键改进！
        self.ctr_embedding = nn.Embedding(2, ctr_embedding_dim)  # 0/1 CTR labels
        nn.init.normal_(self.ctr_embedding.weight, 0.0, 0.02)

        # 噪声编码器 - 包含CTR条件信息
        total_input_dim = noise_dim + ctr_embedding_dim
        self.noise_encoder = FixedNoiseEncoder(total_input_dim, encoded_dim=512)

        # 重新设计维度分配 - 确保总和为512
        if self.num_numeric > 0 and self.num_categorical > 0:
            numeric_dim = 256
            categorical_dim = 256
        elif self.num_numeric > 0:
            numeric_dim = 512
            categorical_dim = 0
        else:
            numeric_dim = 0
            categorical_dim = 512

        self.numeric_dim = numeric_dim
        self.categorical_dim = categorical_dim

        # 特征生成器
        self.numeric_generator = FixedNumericGenerator(
            encoded_dim=numeric_dim,
            num_features=self.num_numeric
        ) if numeric_dim > 0 else None

        self.categorical_generator = FixedCategoricalGenerator(
            encoded_dim=categorical_dim,
            vocab_sizes=vocab_sizes,
            embedding_dim=embedding_dim
        ) if categorical_dim > 0 else None

    def forward(self, noise, ctr_labels, temperature=1.0, hard=False):
        """
        前向传播 - 条件生成
        Args:
            noise: 随机噪声 [batch_size, noise_dim]
            ctr_labels: CTR标签 [batch_size] (0 or 1)
            temperature: Gumbel softmax温度
            hard: 是否使用hard sampling
        """
        batch_size = noise.size(0)

        # 嵌入CTR标签
        ctr_emb = self.ctr_embedding(ctr_labels.long())  # [batch_size, ctr_embedding_dim]

        # 拼接噪声和CTR嵌入
        combined_input = torch.cat([noise, ctr_emb], dim=1)  # [batch_size, noise_dim + ctr_embedding_dim]

        # 编码组合输入
        encoded_noise = self.noise_encoder(combined_input)

        # 分配给不同的生成器
        numeric_output = None
        cat_logits, cat_probs, cat_embeddings = None, None, None

        if self.numeric_dim > 0 and self.numeric_generator is not None:
            numeric_noise = encoded_noise[:, :self.numeric_dim]
            numeric_output = self.numeric_generator(numeric_noise)

        if self.categorical_dim > 0 and self.categorical_generator is not None:
            categorical_noise = encoded_noise[:, -self.categorical_dim:]
            cat_logits, cat_probs, cat_embeddings = self.categorical_generator(
                categorical_noise, temperature, hard
            )

        return {
            'numeric': numeric_output,
            'categorical_logits': cat_logits,
            'categorical_probs': cat_probs,
            'categorical_embeddings': cat_embeddings
        }


class ImprovedDiscriminator(nn.Module):
    """
    改进的判别器 - 更强的架构和更好的CTR预测能力
    """

    def __init__(self, numeric_features: List[str], categorical_features: List[str],
                 vocab_sizes: List[int], embedding_dim: int = 16, hidden_dim: int = 256):
        super(ImprovedDiscriminator, self).__init__()

        self.numeric_features = numeric_features
        self.categorical_features = categorical_features
        self.num_numeric = len(numeric_features)
        self.num_categorical = len(categorical_features)
        self.embedding_dim = embedding_dim
        self.hidden_dim = hidden_dim
        
        # 类别特征的embedding层
        if self.num_categorical > 0:
            self.embeddings = nn.ModuleList([
                nn.Embedding(vocab_size, embedding_dim)
                for vocab_size in vocab_sizes
            ])
            # 初始化embedding权重
            for embedding in self.embeddings:
                nn.init.normal_(embedding.weight, 0.0, 0.02)
        else:
            self.embeddings = None
            
        # 改进的特征处理器 - 动态计算输入维度
        self.total_dim = self.num_numeric + self.num_categorical * embedding_dim

        # 延迟初始化 - 在第一次forward时确定实际维度
        self.feature_processor = None
        self.hidden_dim = hidden_dim
        self._initialized = False

        # 延迟初始化判别头
        self.real_fake_head = None
        self.ctr_head = None

        # 添加一个dummy参数以便get_device()工作
        self.dummy_param = nn.Parameter(torch.zeros(1))
        
    def forward(self, numeric_data=None, categorical_data=None, categorical_embeddings=None):
        features = []
        
        # 获取batch_size
        if numeric_data is not None:
            batch_size = numeric_data.size(0)
        elif categorical_data is not None:
            batch_size = categorical_data.size(0)
        elif categorical_embeddings is not None:
            batch_size = categorical_embeddings[0].size(0)
        else:
            raise ValueError("No input data provided")
        
        # 处理数值特征
        if numeric_data is not None and self.num_numeric > 0:
            features.append(numeric_data)
        elif self.num_numeric > 0:
            features.append(torch.zeros(batch_size, self.num_numeric, device=self.get_device()))
            
        # 处理类别特征
        if categorical_embeddings is not None:
            # 来自Generator的soft embedding
            valid_embeddings = [emb for emb in categorical_embeddings if emb is not None]
            if valid_embeddings:
                cat_emb = torch.cat(valid_embeddings, dim=-1)
                features.append(cat_emb)
        elif categorical_data is not None and self.embeddings is not None:
            # 真实数据的hard embedding
            cat_emb_list = []
            for i, embedding_layer in enumerate(self.embeddings):
                emb = embedding_layer(categorical_data[:, i])
                cat_emb_list.append(emb)
            cat_emb = torch.cat(cat_emb_list, dim=-1)
            features.append(cat_emb)
        elif self.num_categorical > 0:
            features.append(torch.zeros(batch_size, self.num_categorical * self.embedding_dim, 
                                      device=self.get_device()))
            
        # 拼接所有特征
        if features:
            combined_features = torch.cat(features, dim=-1)
        else:
            raise ValueError("No input features provided")

        # 动态初始化网络层（如果还未初始化）
        if not self._initialized:
            self._initialize_layers(combined_features.size(1))

        # 特征处理
        processed_features = self.feature_processor(combined_features)
        
        # 多任务预测
        real_fake_score = self.real_fake_head(processed_features)
        ctr_score = self.ctr_head(processed_features)
        
        return real_fake_score, ctr_score, processed_features
    
    def _initialize_layers(self, actual_input_dim):
        """动态初始化网络层"""
        print(f"Initializing discriminator with actual input dim: {actual_input_dim}")

        # 初始化特征处理器
        self.feature_processor = nn.Sequential(
            nn.Linear(actual_input_dim, self.hidden_dim),
            nn.BatchNorm1d(self.hidden_dim),
            nn.LeakyReLU(0.2),
            nn.Dropout(0.3),

            nn.Linear(self.hidden_dim, self.hidden_dim // 2),
            nn.BatchNorm1d(self.hidden_dim // 2),
            nn.LeakyReLU(0.2),
            nn.Dropout(0.3),

            nn.Linear(self.hidden_dim // 2, self.hidden_dim // 4),
            nn.BatchNorm1d(self.hidden_dim // 4),
            nn.LeakyReLU(0.2)
        ).to(self.get_device())

        # 初始化判别头
        self.real_fake_head = nn.Sequential(
            nn.Linear(self.hidden_dim // 4, 32),
            nn.LeakyReLU(0.2),
            nn.Dropout(0.2),
            nn.Linear(32, 16),
            nn.LeakyReLU(0.2),
            nn.Linear(16, 1),
            nn.Sigmoid()
        ).to(self.get_device())

        # 初始化CTR预测头
        self.ctr_head = nn.Sequential(
            nn.Linear(self.hidden_dim // 4, 64),
            nn.LeakyReLU(0.2),
            nn.Dropout(0.2),
            nn.Linear(64, 32),
            nn.LeakyReLU(0.2),
            nn.Dropout(0.1),
            nn.Linear(32, 1),
            nn.Sigmoid()
        ).to(self.get_device())

        self._initialized = True

    def get_device(self):
        """获取模型所在设备"""
        return next(self.parameters()).device


def test_fixed_dimension_models():
    """
    测试修复维度问题的模型
    """
    # 测试参数
    batch_size = 32
    noise_dim = 128
    numeric_features = ['I1', 'I2', 'I3']
    categorical_features = ['C1', 'C2', 'C3', 'C4', 'C5']
    vocab_sizes = [100, 50, 5000, 15000, 20000]
    
    print("创建修复维度问题的模型...")
    
    # 创建修复的模型
    generator = FixedImprovedGenerator(
        noise_dim=noise_dim,
        numeric_features=numeric_features,
        categorical_features=categorical_features,
        vocab_sizes=vocab_sizes
    )
    
    discriminator = WeakDiscriminator(
        numeric_features=numeric_features,
        categorical_features=categorical_features,
        vocab_sizes=vocab_sizes
    )
    
    print("模型创建成功！")
    
    # 测试Generator - 多次生成检查多样性
    noise1 = torch.randn(batch_size, noise_dim)
    noise2 = torch.randn(batch_size, noise_dim)
    
    print("测试Generator...")
    
    try:
        gen_output1 = generator(noise1, temperature=1.0, hard=False)
        gen_output2 = generator(noise2, temperature=1.0, hard=False)
        
        print(f"Generator测试成功！")
        if gen_output1['numeric'] is not None:
            print(f"  数值特征1形状: {gen_output1['numeric'].shape}")
            print(f"  数值特征1范围: [{gen_output1['numeric'].min():.3f}, {gen_output1['numeric'].max():.3f}]")
            print(f"  数值特征1标准差: {gen_output1['numeric'].std(dim=0).mean():.6f}")
            
            # 检查两次生成的差异
            diff = torch.abs(gen_output1['numeric'].mean(dim=0) - gen_output2['numeric'].mean(dim=0)).mean()
            print(f"  两次生成的平均差异: {diff:.6f}")
            
            # 检查每个特征的标准差
            feature_stds = gen_output1['numeric'].std(dim=0)
            print(f"  各特征标准差: {feature_stds.tolist()}")
            print(f"  最小特征标准差: {feature_stds.min():.6f}")
        
        if gen_output1['categorical_embeddings'] is not None:
            print(f"  类别特征embedding数量: {len(gen_output1['categorical_embeddings'])}")
            valid_embeddings = [emb for emb in gen_output1['categorical_embeddings'] if emb is not None]
            if valid_embeddings:
                print(f"  有效embedding数量: {len(valid_embeddings)}")
                print(f"  第一个embedding形状: {valid_embeddings[0].shape}")
        
        # 测试Discriminator
        print("测试Discriminator...")
        d_output_fake = discriminator(
            numeric_data=gen_output1['numeric'],
            categorical_embeddings=gen_output1['categorical_embeddings']
        )
        print(f"Discriminator测试成功！")
        print(f"  真假判别分数形状: {d_output_fake[0].shape}")
        print(f"  CTR预测分数形状: {d_output_fake[1].shape}")
        
        print("\n修复维度问题的模型测试通过！")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    return True


if __name__ == "__main__":
    test_fixed_dimension_models()