#!/usr/bin/env python3
"""
改进的GAN数据预处理器 - 解决特征质量问题
主要改进：
1. 使用标准化而非MinMax归一化，保持统计特性
2. 更好的类别特征处理，减少信息损失
3. 保持特征分布的统计特性
4. 改进的数据质量控制
"""

import os
import logging
import pickle
import json
import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, LabelEncoder
from typing import Dict, List, Tuple, Optional
import torch
from torch.utils.data import TensorDataset, DataLoader

# 导入现有的工具函数
import sys
from pathlib import Path
ROOT_DIR = Path(__file__).resolve().parent.parent.parent
sys.path.append(str(ROOT_DIR))
from src.data_process.utils import get_column


class ImprovedGANDataProcessor:
    """
    改进的GAN数据预处理器
    解决特征质量和统计特性保持问题
    """
    
    def __init__(self, dataset_name: str, data_dir: str, output_dir: str, 
                 max_vocab_size: int = 20000, min_freq: int = 5):
        self.dataset_name = dataset_name
        self.data_dir = data_dir
        self.output_dir = output_dir
        self.max_vocab_size = max_vocab_size
        self.min_freq = min_freq  # 最小频率阈值
        
        os.makedirs(output_dir, exist_ok=True)
        
        # 获取特征定义
        self.numeric_features, self.categorical_features, self.label_col = get_column(dataset_name)
        
        # 预处理器
        self.numeric_scalers = {}  # 每个数值特征一个scaler
        self.categorical_encoders = {}  # 每个类别特征一个encoder
        self.vocab_info = {}  # 词汇表信息
        
        self.logger = logging.getLogger(__name__)
        self.logger.info(f"Initialized ImprovedGANDataProcessor for {dataset_name}")
        self.logger.info(f"Max vocab size: {max_vocab_size}, Min frequency: {min_freq}")
    
    def data_clean(self, df: pd.DataFrame) -> pd.DataFrame:
        """基础数据清洗"""
        df = df.copy()
        
        # 数值特征处理
        if len(self.numeric_features) > 0:
            # 填充缺失值为中位数而非0，保持分布特性
            for col in self.numeric_features:
                if col in df.columns:
                    median_val = df[col].median()
                    df[col] = df[col].fillna(median_val)
                    
                    # 处理异常值 (使用IQR方法)
                    Q1 = df[col].quantile(0.25)
                    Q3 = df[col].quantile(0.75)
                    IQR = Q3 - Q1
                    lower_bound = Q1 - 1.5 * IQR
                    upper_bound = Q3 + 1.5 * IQR
                    
                    # 截断异常值而非删除
                    df[col] = df[col].clip(lower_bound, upper_bound)
        
        # 类别特征处理
        if len(self.categorical_features) > 0:
            for col in self.categorical_features:
                if col in df.columns:
                    # 处理categorical类型
                    if df[col].dtype.name == 'category':
                        # 添加__MISSING__到categories中
                        if '__MISSING__' not in df[col].cat.categories:
                            df[col] = df[col].cat.add_categories(['__MISSING__'])
                        # 填充缺失值
                        df[col] = df[col].fillna('__MISSING__')
                        # 转换为字符串类型
                        df[col] = df[col].astype(str)
                    else:
                        # 非categorical类型直接处理
                        df[col] = df[col].fillna('__MISSING__').astype(str)
        
        return df
    
    def normalize_numeric_features(self, df: pd.DataFrame, is_training: bool = True) -> pd.DataFrame:
        """
        改进的数值特征标准化 - 使用StandardScaler保持统计特性
        """
        df = df.copy()
        
        for col in self.numeric_features:
            if col not in df.columns:
                continue
                
            if is_training:
                # 训练时拟合scaler
                scaler = StandardScaler()
                df[col] = scaler.fit_transform(df[[col]]).flatten()
                self.numeric_scalers[col] = scaler
                
                # 记录统计信息
                self.logger.info(f"Numeric feature {col}: mean={scaler.mean_[0]:.4f}, std={scaler.scale_[0]:.4f}")
            else:
                # 推理时使用已拟合的scaler
                if col in self.numeric_scalers:
                    scaler = self.numeric_scalers[col]
                    df[col] = scaler.transform(df[[col]]).flatten()
                else:
                    self.logger.warning(f"No scaler found for {col}, skipping normalization")
        
        return df
    
    def build_categorical_vocab(self, df: pd.DataFrame, is_training: bool = True) -> pd.DataFrame:
        """
        改进的类别特征词汇表构建 - 更好的频率过滤和信息保持
        """
        df = df.copy()
        
        for col in self.categorical_features:
            if col not in df.columns:
                continue
                
            if is_training:
                # 计算值频率
                value_counts = df[col].value_counts()
                total_unique = len(value_counts)
                
                self.logger.info(f"Feature {col}: {total_unique} unique values")
                
                # 改进的过滤策略
                # 1. 首先按频率过滤
                frequent_values = value_counts[value_counts >= self.min_freq]
                
                # 2. 如果还是太多，保留top-K
                if len(frequent_values) > self.max_vocab_size:
                    self.logger.warning(f"Feature {col}: {len(frequent_values)} frequent values, reducing to {self.max_vocab_size}")
                    frequent_values = frequent_values.head(self.max_vocab_size)
                
                # 3. 计算覆盖率
                covered_samples = frequent_values.sum()
                coverage = covered_samples / len(df) * 100
                
                # 构建词汇表
                vocab_values = frequent_values.index.tolist()
                
                # 使用LabelEncoder而非手动映射，更稳定
                encoder = LabelEncoder()
                # 添加特殊标记
                all_values = ['__UNKNOWN__'] + vocab_values
                encoder.fit(all_values)
                
                self.categorical_encoders[col] = encoder
                self.vocab_info[col] = {
                    'vocab_size': len(all_values),
                    'original_unique_count': total_unique,
                    'coverage': coverage,
                    'min_freq': self.min_freq,
                    'vocab_values': vocab_values
                }
                
                self.logger.info(f"Categorical feature {col}: final_vocab_size={len(all_values)}, coverage={coverage:.2f}%")
            
            # 编码类别值
            if col in self.categorical_encoders:
                encoder = self.categorical_encoders[col]
                # 将未知值映射到__UNKNOWN__
                df[f"{col}_idx"] = df[col].apply(
                    lambda x: x if x in encoder.classes_ else '__UNKNOWN__'
                ).pipe(encoder.transform)
            else:
                self.logger.warning(f"No encoder found for {col}, skipping encoding")
        
        return df
    
    def preprocess_for_gan(self, df: pd.DataFrame, is_training: bool = True) -> pd.DataFrame:
        """
        改进的GAN预处理流程
        """
        self.logger.info(f"Starting improved GAN preprocessing for {len(df)} samples")
        
        # 1. 数据清洗
        df = self.data_clean(df)
        
        # 2. 数值特征标准化 (改进)
        df = self.normalize_numeric_features(df, is_training)
        
        # 3. 类别特征编码 (改进)
        df = self.build_categorical_vocab(df, is_training)
        
        # 4. 数据质量检查
        self._quality_check(df)
        
        # 5. 保存预处理信息
        if is_training:
            self.save_preprocessing_info()
        
        self.logger.info("Improved GAN preprocessing completed")
        return df
    
    def _quality_check(self, df: pd.DataFrame):
        """数据质量检查"""
        # 检查数值特征的分布
        for col in self.numeric_features:
            if col in df.columns:
                std_val = df[col].std()
                if std_val < 0.1:
                    self.logger.warning(f"Numeric feature {col} has low variance: {std_val:.6f}")
        
        # 检查类别特征的多样性
        for col in self.categorical_features:
            idx_col = f"{col}_idx"
            if idx_col in df.columns:
                unique_count = df[idx_col].nunique()
                if unique_count < 5:
                    self.logger.warning(f"Categorical feature {col} has low diversity: {unique_count} unique values")
        
        # 检查CTR分布
        if self.label_col in df.columns:
            ctr_rate = df[self.label_col].mean()
            self.logger.info(f"CTR rate: {ctr_rate:.4f}")
            if ctr_rate < 0.01 or ctr_rate > 0.5:
                self.logger.warning(f"Unusual CTR rate: {ctr_rate:.4f}")
    
    def reverse_preprocessing(self, synthetic_df: pd.DataFrame) -> pd.DataFrame:
        """
        改进的反预处理 - 将合成数据转换回原始格式
        """
        result_df = synthetic_df.copy()
        
        # 1. 数值特征反标准化
        for col in self.numeric_features:
            if col in result_df.columns and col in self.numeric_scalers:
                scaler = self.numeric_scalers[col]
                result_df[col] = scaler.inverse_transform(result_df[[col]]).flatten()
        
        # 2. 类别特征反编码
        for col in self.categorical_features:
            idx_col = f"{col}_idx"
            if idx_col in result_df.columns and col in self.categorical_encoders:
                encoder = self.categorical_encoders[col]
                # 确保索引在有效范围内
                valid_indices = np.clip(result_df[idx_col].astype(int), 0, len(encoder.classes_) - 1)
                result_df[col] = encoder.inverse_transform(valid_indices)
                
                # 删除索引列
                result_df = result_df.drop(columns=[idx_col])
        
        return result_df
    
    def get_feature_info(self) -> Dict:
        """获取特征信息"""
        vocab_sizes = [self.vocab_info[col]['vocab_size'] for col in self.categorical_features]
        
        return {
            'numeric_features': self.numeric_features,
            'categorical_features': self.categorical_features,
            'vocab_sizes': vocab_sizes,
            'label_col': self.label_col,
            'num_numeric': len(self.numeric_features),
            'num_categorical': len(self.categorical_features)
        }
    
    def get_vocab_sizes(self) -> List[int]:
        """获取词汇表大小列表"""
        return [self.vocab_info[col]['vocab_size'] for col in self.categorical_features]
    
    def save_preprocessing_info(self):
        """保存预处理信息"""
        info = {
            'numeric_scalers': self.numeric_scalers,
            'categorical_encoders': self.categorical_encoders,
            'vocab_info': self.vocab_info,
            'feature_info': self.get_feature_info(),
            'config': {
                'max_vocab_size': self.max_vocab_size,
                'min_freq': self.min_freq
            }
        }
        
        # 保存为pickle
        save_path = os.path.join(self.output_dir, 'improved_gan_preprocessing_info.pkl')
        with open(save_path, 'wb') as f:
            pickle.dump(info, f)
        
        self.logger.info(f"Improved preprocessing info saved to {save_path}")
    
    def load_preprocessing_info(self, file_path=None):
        """加载预处理信息"""
        if file_path is None:
            save_path = os.path.join(self.output_dir, 'improved_gan_preprocessing_info.pkl')
        else:
            save_path = file_path

        if not os.path.exists(save_path):
            raise FileNotFoundError(f"Preprocessing info not found at {save_path}")

        with open(save_path, 'rb') as f:
            info = pickle.load(f)

        self.numeric_scalers = info['numeric_scalers']
        self.categorical_encoders = info['categorical_encoders']
        self.vocab_info = info['vocab_info']

        self.logger.info(f"Improved preprocessing info loaded from {save_path}")
        return info['feature_info']


def create_improved_dataloader(processed_df: pd.DataFrame, config, shuffle: bool = True) -> DataLoader:
    """
    创建改进的数据加载器
    """
    # 准备数值特征
    numeric_data = None
    if len(processed_df.columns) > 0:
        numeric_cols = [col for col in processed_df.columns if not col.endswith('_idx') and col != 'Label']
        if numeric_cols:
            numeric_data = torch.FloatTensor(processed_df[numeric_cols].values)
    
    # 准备类别特征
    categorical_data = None
    categorical_cols = [col for col in processed_df.columns if col.endswith('_idx')]
    if categorical_cols:
        categorical_data = torch.LongTensor(processed_df[categorical_cols].values)
    
    # 准备标签
    label_col = 'Label' if 'Label' in processed_df.columns else processed_df.columns[-1]
    labels = torch.FloatTensor(processed_df[label_col].values)
    
    # 创建数据集
    if numeric_data is not None and categorical_data is not None:
        dataset = TensorDataset(numeric_data, categorical_data, labels)
    elif numeric_data is not None:
        dataset = TensorDataset(numeric_data, torch.zeros(len(numeric_data), 1), labels)
    elif categorical_data is not None:
        dataset = TensorDataset(torch.zeros(len(categorical_data), 1), categorical_data, labels)
    else:
        raise ValueError("No valid features found")
    
    return DataLoader(dataset, batch_size=config.batch_size, shuffle=shuffle, num_workers=2)


def prepare_improved_gan_data(dataset_name: str, data_dir: str, output_dir: str, 
                             train_file: str = 'train.csv', use_cache: bool = True,
                             max_samples: Optional[int] = None, max_vocab_size: int = 20000,
                             min_freq: int = 5) -> Tuple[pd.DataFrame, ImprovedGANDataProcessor]:
    """
    准备改进的GAN训练数据
    """
    # 缓存文件名
    cache_suffix = f"_improved_{max_samples}_{max_vocab_size}_{min_freq}" if max_samples else f"_improved_full_{max_vocab_size}_{min_freq}"
    cache_file = os.path.join(output_dir, f'processed_{dataset_name}_train{cache_suffix}.pkl')
    
    # 检查缓存
    if use_cache and os.path.exists(cache_file):
        logging.info(f"Loading cached improved data from {cache_file}")
        
        with open(cache_file, 'rb') as f:
            cache_data = pickle.load(f)
        
        processed_df = cache_data['processed_data']
        
        # 重建处理器
        processor = ImprovedGANDataProcessor(dataset_name, data_dir, output_dir, max_vocab_size, min_freq)
        processor.numeric_scalers = cache_data['numeric_scalers']
        processor.categorical_encoders = cache_data['categorical_encoders']
        processor.vocab_info = cache_data['vocab_info']
        
        logging.info(f"Loaded {len(processed_df)} cached improved samples")
        return processed_df, processor
    
    # 新处理
    processor = ImprovedGANDataProcessor(dataset_name, data_dir, output_dir, max_vocab_size, min_freq)
    
    # 读取数据
    train_path = os.path.join(data_dir, train_file)
    
    # 确定使用的列和数据类型
    use_cols = processor.numeric_features + processor.categorical_features + [processor.label_col]
    dtype_dict = {}
    dtype_dict.update({feat: 'float32' for feat in processor.numeric_features})
    dtype_dict.update({feat: 'category' for feat in processor.categorical_features})
    dtype_dict[processor.label_col] = 'float32'
    
    # 读取数据
    df = pd.read_csv(train_path, usecols=use_cols, dtype=dtype_dict, nrows=max_samples)
    logging.info(f"Loaded {len(df)} samples from {train_path}")
    
    # 预处理
    processed_df = processor.preprocess_for_gan(df, is_training=True)
    
    # 保存缓存
    if use_cache:
        cache_data = {
            'processed_data': processed_df,
            'numeric_scalers': processor.numeric_scalers,
            'categorical_encoders': processor.categorical_encoders,
            'vocab_info': processor.vocab_info,
            'sample_info': {
                'max_samples': max_samples,
                'max_vocab_size': max_vocab_size,
                'min_freq': min_freq,
                'actual_samples': len(processed_df),
                'ctr_rate': df[processor.label_col].mean()
            }
        }
        
        with open(cache_file, 'wb') as f:
            pickle.dump(cache_data, f)
        
        logging.info(f"Saved improved cache to {cache_file}")
    
    return processed_df, processor
